package org.apache.fineract.portfolio.sacco.savingsaccount.domain;

import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SaccoSavingsAccountRepository extends JpaRepository<SaccoSavingsAccount, Long> {

    @Query("select s from SaccoSavingsAccount s where s.savingsAccount.id = :savingsAccountId")
    Optional<SaccoSavingsAccount> findBySavingsAccountId(@Param("savingsAccountId") Long id);

    @Query("select s from SaccoSavingsAccount s where s.shareAccount.id = ?1")
    Optional<SaccoSavingsAccount> findByShareAccountId(Long id);

    @Query("select s from SaccoSavingsAccount s where s.savingsAccount.product.compulsory = true")
    List<SaccoSavingsAccount> findByCompulsoryProduct();

}
