<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="fineract" id="1">
        <createTable tableName="m_loan_installment_delinquency_tag_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="delinquency_range_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="loan_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="installment_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="addedon_date" type="DATE">
                <constraints nullable="false" />
            </column>
            <column name="first_overdue_date" type="DATE">
                <constraints nullable="false" />
            </column>
            <column name="outstanding_amount" type="DECIMAL(19, 6)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NULL" name="liftedon_date" type="DATE" />
            <column name="created_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="version" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
        </createTable>
    </changeSet>
    <changeSet author="fineract" id="2" context="mysql">
        <addColumn tableName="m_loan_installment_delinquency_tag_history">
            <column name="created_on_utc" type="DATETIME(6)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on_utc" type="DATETIME(6)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="fineract" id="3" context="postgresql">
        <addColumn tableName="m_loan_installment_delinquency_tag_history">
            <column name="created_on_utc" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_on_utc" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="fineract" id="4">
        <addForeignKeyConstraint baseColumnNames="delinquency_range_id" baseTableName="m_loan_installment_delinquency_tag_history"
                                 constraintName="FK_m_delinquency_installment_tags_range" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="m_delinquency_range" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="loan_id" baseTableName="m_loan_installment_delinquency_tag_history"
                                 constraintName="FK_m_delinquency_installment_tags_loan" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="m_loan" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="installment_id" baseTableName="m_loan_installment_delinquency_tag_history"
                                 constraintName="FK_m_delinquency_installment_tags_installment" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="m_loan_repayment_schedule" validate="true"/>
    </changeSet>
</databaseChangeLog>
