package org.apache.fineract.portfolio.sacco.savingsproduct.domain;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface SaccoSavingsProductRepository extends JpaRepository<SaccoSavingsProduct, Long> {

    @Query("select s from SaccoSavingsProduct s where s.savingsProduct.id = ?1")
    Optional<SaccoSavingsProduct> findBySavingsProductId(Long id);

    @Query("select s from SaccoSavingsProduct s where s.shareProduct.id = ?1")
    Optional<SaccoSavingsProduct> findByShareProductId(Long id);
}
