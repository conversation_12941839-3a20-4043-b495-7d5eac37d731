//
// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements. See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership. The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License. You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied. See the License for the
// specific language governing permissions and limitations
// under the License.
//

:doctitle: Fineract Platform Documentation

include::config.adoc[]

= Fineract Documentation

include::license.adoc[leveloffset=+1]

include::preface.adoc[leveloffset=+1]

include::colophon.adoc[leveloffset=+1]

include::introduction.adoc[leveloffset=+1]

include::chapters/deployment/index.adoc[leveloffset=+1]

include::chapters/architecture/index.adoc[leveloffset=+1]

include::chapters/development/index.adoc[leveloffset=+1]

include::chapters/custom/index.adoc[leveloffset=+1]

include::chapters/resilience/index.adoc[leveloffset=+1]

include::chapters/security/index.adoc[leveloffset=+1]

include::chapters/testing/index.adoc[leveloffset=+1]

include::chapters/documentation/index.adoc[leveloffset=+1]

include::chapters/release/index.adoc[leveloffset=+1]

include::chapters/sdk/index.adoc[leveloffset=+1]

include::fineract-client.adoc[leveloffset=+1]

include::faq.adoc[leveloffset=+1]

include::glossary.adoc[leveloffset=+1]

include::indices.adoc[leveloffset=+1]

include::chapters/appendix/index.adoc[]
