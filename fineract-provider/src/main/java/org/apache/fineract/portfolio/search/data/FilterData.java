/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.portfolio.search.data;

import static org.apache.fineract.infrastructure.core.service.database.SqlOperator.BTW;
import static org.apache.fineract.infrastructure.core.service.database.SqlOperator.EQ;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.fineract.infrastructure.core.service.database.SqlOperator;

/**
 * Immutable data object representing datatable data.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public final class FilterData implements Serializable {

    private SqlOperator operator;

    private List<String> values;

    static FilterData eq(String value) {
        return new FilterData(EQ, List.of(value));
    }

    static FilterData btw(String value1, String value2) {
        return new FilterData(BTW, List.of(value1, value2));
    }

    static FilterData create(SqlOperator op, String... values) {
        return new FilterData(op, List.of(values));
    }
}
