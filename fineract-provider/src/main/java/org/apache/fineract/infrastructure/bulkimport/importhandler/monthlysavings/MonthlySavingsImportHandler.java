/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.infrastructure.bulkimport.importhandler.monthlysavings;

import com.google.gson.GsonBuilder;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.fineract.commands.domain.CommandWrapper;
import org.apache.fineract.commands.service.CommandWrapperBuilder;
import org.apache.fineract.commands.service.PortfolioCommandSourceWritePlatformService;
import org.apache.fineract.infrastructure.bulkimport.constants.MonthlySavingsConstants;
import org.apache.fineract.infrastructure.bulkimport.constants.TemplatePopulateImportConstants;
import org.apache.fineract.infrastructure.bulkimport.data.Count;
import org.apache.fineract.infrastructure.bulkimport.importhandler.ImportHandler;
import org.apache.fineract.infrastructure.bulkimport.importhandler.ImportHandlerUtils;
import org.apache.fineract.infrastructure.bulkimport.importhandler.helper.DateSerializer;
import org.apache.fineract.infrastructure.core.data.ApiParameterError;
import org.apache.fineract.infrastructure.core.domain.AbstractPersistableCustom;
import org.apache.fineract.infrastructure.core.exception.PlatformApiDataValidationException;
import org.apache.fineract.infrastructure.core.serialization.GoogleGsonSerializerHelper;
import org.apache.fineract.portfolio.client.domain.Client;
import org.apache.fineract.portfolio.client.domain.ClientRepository;
import org.apache.fineract.portfolio.savings.data.MonthlySavingsAccountDetailImportData;
import org.apache.fineract.portfolio.savings.domain.SavingsAccount;
import org.apache.fineract.portfolio.savings.domain.SavingsAccountRepository;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MonthlySavingsImportHandler implements ImportHandler {

    private final PortfolioCommandSourceWritePlatformService commandsSourceWritePlatformService;
    private final ClientRepository clientRepository;
    private final SavingsAccountRepository savingsAccountRepository;

    @Autowired
    public MonthlySavingsImportHandler(final PortfolioCommandSourceWritePlatformService commandsSourceWritePlatformService,
            ClientRepository clientRepository, SavingsAccountRepository savingsAccountRepository) {
        this.commandsSourceWritePlatformService = commandsSourceWritePlatformService;
        this.clientRepository = clientRepository;
        this.savingsAccountRepository = savingsAccountRepository;
    }

    @Override
    public Count process(final Workbook workbook, final String locale, final String dateFormat) {
        List<MonthlySavings> monthlySavings = readExcelFile(workbook, locale, dateFormat);
        return importEntity(workbook, monthlySavings, dateFormat);
    }

    private List<MonthlySavings> readExcelFile(final Workbook workbook, final String locale, final String dateFormat) {
        List<MonthlySavings> monthlySavings = new ArrayList<>();
        Sheet monthlySheet = workbook.getSheet(TemplatePopulateImportConstants.MONTHLY_SAVINGS_SHEET_NAME);
        Integer noOfEntries = ImportHandlerUtils.getNumberOfRows(monthlySheet, 0);
        for (int rowIndex = 1; rowIndex <= noOfEntries; rowIndex++) {
            Row row;
            row = monthlySheet.getRow(rowIndex);
            if (ImportHandlerUtils.isNotImported(row, MonthlySavingsConstants.STATUS_COL)) {
                monthlySavings.add(readMonthlySavings(row, locale, dateFormat));
            }
        }
        return monthlySavings;
    }

    private MonthlySavings readMonthlySavings(Row row, final String locale, final String dateFormat) {
        String clientAccountNumber = ImportHandlerUtils.readAsString(MonthlySavingsConstants.CLIENT_NUMBER_COL, row);
        String accountNumber = ImportHandlerUtils.readAsString(MonthlySavingsConstants.ACCOUNT_NUMBER_COL, row);
        LocalDate startDate = ImportHandlerUtils.readAsDate(MonthlySavingsConstants.START_DATE_COL, row);
        BigDecimal amount = BigDecimal.valueOf(ImportHandlerUtils.readAsDouble(MonthlySavingsConstants.AMOUNT_COL, row));

        MonthlySavingsAccountDetailImportData detail = MonthlySavingsAccountDetailImportData.builder().dateFormat(dateFormat)
                .monthlyAmount(amount).startDate(startDate).locale(locale).build();
        return MonthlySavings.builder().locale(locale).dateFormat(dateFormat).rowIndex(row.getRowNum()).accountNumber(accountNumber)
                .clientAccountNumber(clientAccountNumber).monthlySavingsAccountDetailImportData(detail).build();
    }

    private Count importEntity(final Workbook workbook, final List<MonthlySavings> monthlySavings, final String dateFormat) {
        Sheet monthlySheet = workbook.getSheet(TemplatePopulateImportConstants.MONTHLY_SAVINGS_SHEET_NAME);
        GsonBuilder gsonBuilder = GoogleGsonSerializerHelper.createGsonBuilder();
        gsonBuilder.registerTypeAdapter(LocalDate.class, new DateSerializer(dateFormat));

        int successCount = 0;
        int errorCount = 0;
        String errorMessage;
        for (MonthlySavings monthlySaving : monthlySavings) {
            try {
                String payload = gsonBuilder.create().toJson(monthlySaving.getMonthlySavingsAccountDetailImportData());
                Client client = clientRepository.findByAccountNumber(monthlySaving.clientAccountNumber)
                        .orElseThrow(() -> new RuntimeException(
                                String.format("Failed to find client account with account number %s", monthlySaving.clientAccountNumber)));
                Long savingsId = client.getSavingsAccountId();

                if (StringUtils.isNotBlank(monthlySaving.accountNumber)) {
                    savingsId = savingsAccountRepository.findSavingsAccountByAccountNumberOptional(monthlySaving.accountNumber)
                            .map(AbstractPersistableCustom::getId).orElseThrow(() -> new RuntimeException(
                                    String.format("Failed to find savings account with account number %s", monthlySaving.accountNumber)));
                } else if (savingsId == null) {
                    savingsId = savingsAccountRepository.findSavingAccountByClientId(client.getId()).stream()
                            .filter(SavingsAccount::isActive).findFirst().map(AbstractPersistableCustom::getId)
                            .orElseThrow(() -> new RuntimeException(
                                    String.format("Failed to find savings account with account number %s", monthlySaving.accountNumber)));
                }

                final CommandWrapper commandRequest = new CommandWrapperBuilder() //
                        .addMonthlySavingsAccountImport(savingsId) //
                        .withJson(payload) //
                        .build(); //
                commandsSourceWritePlatformService.logCommandSource(commandRequest);
                successCount++;
                Cell statusCell = monthlySheet.getRow(monthlySaving.getRowIndex()).createCell(MonthlySavingsConstants.STATUS_COL);
                statusCell.setCellValue(TemplatePopulateImportConstants.STATUS_CELL_IMPORTED);
                statusCell.setCellStyle(ImportHandlerUtils.getCellStyle(workbook, IndexedColors.LIGHT_GREEN));
            } catch (PlatformApiDataValidationException ex) {
                errorCount++;
                log.error("Problem occurred in importEntity function", ex);
                if (ex.getErrors() != null && !ex.getErrors().isEmpty()) {
                    errorMessage = String.join(",", ex.getErrors().stream().map(ApiParameterError::getDefaultUserMessage).toList());
                    ImportHandlerUtils.writeErrorMessage(monthlySheet, monthlySaving.getRowIndex(), errorMessage,
                            MonthlySavingsConstants.STATUS_COL);

                } else {
                    errorMessage = ImportHandlerUtils.getErrorMessage(ex);
                    ImportHandlerUtils.writeErrorMessage(monthlySheet, monthlySaving.getRowIndex(), errorMessage,
                            MonthlySavingsConstants.STATUS_COL);

                }
            } catch (RuntimeException ex) {
                errorCount++;
                log.error("Problem occurred in importEntity function", ex);
                errorMessage = ImportHandlerUtils.getErrorMessage(ex);
                ImportHandlerUtils.writeErrorMessage(monthlySheet, monthlySaving.getRowIndex(), errorMessage,
                        MonthlySavingsConstants.STATUS_COL);
            }
        }
        monthlySheet.setColumnWidth(MonthlySavingsConstants.STATUS_COL, TemplatePopulateImportConstants.SMALL_COL_SIZE);
        ImportHandlerUtils.writeString(MonthlySavingsConstants.STATUS_COL, monthlySheet.getRow(0),
                TemplatePopulateImportConstants.STATUS_COL_REPORT_HEADER);
        return Count.instance(successCount, errorCount);
    }

    @Data
    @Builder
    private static class MonthlySavings {

        private String clientAccountNumber;

        private String accountNumber;

        private transient Integer rowIndex;

        private String locale;

        private String dateFormat;

        private MonthlySavingsAccountDetailImportData monthlySavingsAccountDetailImportData;

    }
}
